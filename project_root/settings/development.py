"""
Development Settings for CozyWish

This module contains settings specific to the development environment.
It inherits from base.py and overrides settings for local development.

Features:
- Debug mode enabled
- Console email backend
- SQLite database
- Local file storage
- Relaxed security settings
- Development-specific middleware
"""

from .base import *
import os

# --- Development Environment Override ---
DEBUG = True
ENABLE_TEST_VIEW = True

# --- Development Hosts ---
ALLOWED_HOSTS = ['localhost', '127.0.0.1', 'testserver', '0.0.0.0']

# --- Development CSRF Settings ---
CSRF_TRUSTED_ORIGINS = [
    'http://localhost:8000',
    'http://127.0.0.1:8000',
]

# --- Development Database ---
# Force SQLite for development regardless of DATABASE_URL
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# --- Development Email Backend ---
# Always use console backend in development unless forced
FORCE_EMAIL_BACKEND = env('FORCE_EMAIL_BACKEND', default=False)
if FORCE_EMAIL_BACKEND:
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
    EMAIL_HOST_PASSWORD = env('EMAIL_HOST_PASSWORD', default='')
else:
    EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# --- Development Media Files ---
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# --- Development Storage Configuration ---
STORAGES = {
    "default": {
        "BACKEND": "django.core.files.storage.FileSystemStorage",
    },
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
}

# --- Development Cache ---
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'cozywish-dev-cache',
        'TIMEOUT': 60,  # Shorter timeout for development
        'OPTIONS': {
            'MAX_ENTRIES': 100,
            'CULL_FREQUENCY': 3,
        }
    }
}

# --- Development Security Settings ---
# Relaxed security for development
SECURE_SSL_REDIRECT = False
SECURE_HSTS_SECONDS = 0
SECURE_HSTS_INCLUDE_SUBDOMAINS = False
SECURE_HSTS_PRELOAD = False
SECURE_CONTENT_TYPE_NOSNIFF = False
SECURE_BROWSER_XSS_FILTER = False
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False

# --- Development Logging ---
LOGGING['loggers']['django']['level'] = 'INFO'
# Set debug level for all app loggers
for app in ['accounts_app', 'venues_app', 'utility_app', 'utils', 'booking_cart_app',
           'payments_app', 'dashboard_app', 'review_app', 'admin_app',
           'discount_app', 'notifications_app']:
    if app in LOGGING['loggers']:
        LOGGING['loggers'][app]['level'] = 'DEBUG'

# --- Development Celery ---
# Use eager execution for development
CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True

# --- Development Debug Toolbar (if installed) ---
try:
    import debug_toolbar
    INSTALLED_APPS += ['debug_toolbar']
    MIDDLEWARE = ['debug_toolbar.middleware.DebugToolbarMiddleware'] + MIDDLEWARE
    INTERNAL_IPS = ['127.0.0.1', 'localhost']
    DEBUG_TOOLBAR_CONFIG = {
        'SHOW_TOOLBAR_CALLBACK': lambda request: DEBUG,
    }
except ImportError:
    pass

# --- Development Environment Indicator ---
ENVIRONMENT_NAME = 'Development'
ENVIRONMENT_COLOR = '#28a745'  # Green
