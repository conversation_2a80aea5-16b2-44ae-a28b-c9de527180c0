"""
Production Settings for CozyWish

This module contains settings specific to the production environment.
It inherits from base.py and overrides settings for production deployment.

Features:
- Debug mode disabled
- SMTP email backend
- PostgreSQL database
- AWS S3 storage
- Enhanced security settings
- Production-optimized caching
- Comprehensive logging
"""

from .base import *
import os

# --- Production Environment Override ---
DEBUG = False
ENABLE_TEST_VIEW = False

# --- Production Hosts ---
ALLOWED_HOSTS = ['.cozywish.com', 'cozywish.onrender.com']
RENDER_EXTERNAL_HOSTNAME = env('RENDER_EXTERNAL_HOSTNAME', default=None)
if RENDER_EXTERNAL_HOSTNAME:
    ALLOWED_HOSTS.append(RENDER_EXTERNAL_HOSTNAME)

# --- Production CSRF Settings ---
CSRF_TRUSTED_ORIGINS = [
    'https://www.cozywish.com',
    'https://cozywish.onrender.com'
]

# --- Production Database ---
# Require DATABASE_URL in production
DATABASE_URL = env('DATABASE_URL')
if not DATABASE_URL:
    raise environ.ImproperlyConfigured(
        "DATABASE_URL is required in production environment"
    )
DATABASES = {'default': dj_database_url.parse(DATABASE_URL, conn_max_age=600)}

# --- Production Email Backend ---
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST_PASSWORD = env('EMAIL_HOST_PASSWORD')

# Validate email configuration
if not EMAIL_HOST_PASSWORD:
    raise environ.ImproperlyConfigured(
        "EMAIL_HOST_PASSWORD is required in production environment"
    )

# --- Production Media Files (AWS S3) ---
# Require AWS credentials in production
AWS_ACCESS_KEY_ID = env('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = env('AWS_SECRET_ACCESS_KEY')
AWS_STORAGE_BUCKET_NAME = env('AWS_STORAGE_BUCKET_NAME')

if not all([AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_STORAGE_BUCKET_NAME]):
    missing_creds = []
    if not AWS_ACCESS_KEY_ID:
        missing_creds.append('AWS_ACCESS_KEY_ID')
    if not AWS_SECRET_ACCESS_KEY:
        missing_creds.append('AWS_SECRET_ACCESS_KEY')
    if not AWS_STORAGE_BUCKET_NAME:
        missing_creds.append('AWS_STORAGE_BUCKET_NAME')
    
    raise environ.ImproperlyConfigured(
        f"AWS S3 credentials are required in production. Missing: {', '.join(missing_creds)}"
    )

# --- Production Storage Configuration ---
STORAGES = {
    "default": {
        "BACKEND": "storages.backends.s3boto3.S3Boto3Storage",
        "OPTIONS": {
            "access_key": AWS_ACCESS_KEY_ID,
            "secret_key": AWS_SECRET_ACCESS_KEY,
            "bucket_name": AWS_STORAGE_BUCKET_NAME,
            "region_name": env('AWS_S3_REGION_NAME', default='us-east-1'),
            "custom_domain": env('AWS_S3_CUSTOM_DOMAIN', default=None),
            "file_overwrite": False,
            "default_acl": None,
            "signature_version": "s3v4",
            "addressing_style": "virtual",
            "use_ssl": True,
            "verify": True,
            "object_parameters": {
                "CacheControl": "max-age=86400",
            },
            "querystring_auth": True,
            "querystring_expire": 3600,
        },
    },
    "staticfiles": {
        "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
    },
}

# --- Production Static Files ---
WHITENOISE_USE_FINDERS = True
WHITENOISE_AUTOREFRESH = False

# --- Production Cache ---
# Use Redis for production caching if available
REDIS_URL = env('REDIS_URL', default=None)
if REDIS_URL:
    CACHES = {
        'default': {
            'BACKEND': 'django_redis.cache.RedisCache',
            'LOCATION': REDIS_URL,
            'OPTIONS': {
                'CLIENT_CLASS': 'django_redis.client.DefaultClient',
                'CONNECTION_POOL_KWARGS': {
                    'max_connections': 20,
                    'retry_on_timeout': True,
                },
            },
            'KEY_PREFIX': 'cozywish',
            'TIMEOUT': 300,
        }
    }
else:
    # Fallback to locmem cache
    CACHES = {
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'cozywish-prod-cache',
            'TIMEOUT': 300,
            'OPTIONS': {
                'MAX_ENTRIES': 1000,
                'CULL_FREQUENCY': 3,
            }
        }
    }

# --- Production Security Settings ---
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = ********  # 1 year
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

# --- Production Logging ---
LOGGING['loggers']['django']['level'] = 'WARNING'
# Set info level for all app loggers in production
for app in ['accounts_app', 'venues_app', 'utility_app', 'utils', 'booking_cart_app',
           'payments_app', 'dashboard_app', 'review_app', 'admin_app',
           'discount_app', 'notifications_app']:
    if app in LOGGING['loggers']:
        LOGGING['loggers'][app]['level'] = 'INFO'

# --- Production Celery ---
CELERY_TASK_ALWAYS_EAGER = False
CELERY_TASK_EAGER_PROPAGATES = False

# --- Production Environment Indicator ---
ENVIRONMENT_NAME = 'Production'
ENVIRONMENT_COLOR = '#dc3545'  # Red
